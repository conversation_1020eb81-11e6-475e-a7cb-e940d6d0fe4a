node_modules
.DS_Store
dist
dist-ssr
*.local
.eslintcache
report.html
vite.config.*.timestamp*

yarn.lock
npm-debug.log*
.pnpm-error.log*
.pnpm-debug.log
tests/**/coverage/

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

.kiro

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# npm
package-lock.json

# yarn
yarn-error.log
.pnp.*
.pnp.js

# pnpm
.pnpm-debug.log

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo